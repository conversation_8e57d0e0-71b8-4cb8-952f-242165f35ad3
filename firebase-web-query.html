<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Database Query Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #ff6b35;
            text-align: center;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #ff6b35;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #e55a2b;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .results {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .config-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Firebase Database Query Tool</h1>
        
        <div class="config-section">
            <h3>📋 Configuration</h3>
            <p><strong>Project ID:</strong> <span id="projectId">bakedbot-agents</span></p>
            <p><strong>Status:</strong> <span id="connectionStatus">Not connected</span></p>
            <button onclick="initializeFirebase()">Initialize Firebase</button>
        </div>

        <div class="section">
            <h3>📚 Firestore Collections</h3>
            <button onclick="listCollections()">List All Collections</button>
            <div id="collectionsResult" class="results" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>📄 Query Firestore Collection</h3>
            <input type="text" id="collectionName" placeholder="Collection name (e.g., users)" />
            <input type="number" id="queryLimit" placeholder="Limit (default: 10)" value="10" />
            <button onclick="queryCollection()">Query Collection</button>
            <div id="queryResult" class="results" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>🔍 Get Specific Document</h3>
            <input type="text" id="docCollection" placeholder="Collection name" />
            <input type="text" id="docId" placeholder="Document ID" />
            <button onclick="getDocument()">Get Document</button>
            <div id="docResult" class="results" style="display:none;"></div>
        </div>

        <div class="section">
            <h3>👥 Firebase Auth Users</h3>
            <p><em>Note: This requires Firebase Admin SDK and cannot be done from client-side code for security reasons.</em></p>
            <p>Use the Firebase Console or server-side scripts to view users.</p>
        </div>

        <div class="section">
            <h3>📊 Realtime Database</h3>
            <input type="text" id="rtdbPath" placeholder="Path (e.g., /users)" value="/" />
            <button onclick="queryRealtimeDB()">Query Realtime DB</button>
            <div id="rtdbResult" class="results" style="display:none;"></div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js';
        import { getFirestore, collection, getDocs, doc, getDoc, limit, query } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js';
        import { getDatabase, ref, get } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-database.js';

        // Firebase configuration from your .env file
        const firebaseConfig = {
            apiKey: "AIzaSyDycX6dSn1IbJr6bMTLCVaLs9wczL_dXkI",
            authDomain: "bakedbot-agents.firebaseapp.com",
            projectId: "bakedbot-agents",
            storageBucket: "bakedbot-agents.appspot.com",
            messagingSenderId: "561517685610",
            appId: "1:561517685610:web:fc764d04813e581ffb5313",
            measurementId: "G-L4NT1SVX47"
        };

        let app, db, rtdb;

        window.initializeFirebase = function() {
            try {
                app = initializeApp(firebaseConfig);
                db = getFirestore(app);
                rtdb = getDatabase(app);
                
                document.getElementById('connectionStatus').textContent = 'Connected ✅';
                document.getElementById('connectionStatus').style.color = 'green';
                showSuccess('Firebase initialized successfully!');
            } catch (error) {
                showError('Failed to initialize Firebase: ' + error.message);
            }
        };

        window.listCollections = async function() {
            if (!db) {
                showError('Please initialize Firebase first');
                return;
            }

            try {
                // Note: listCollections() is not available in client SDK
                // This is a limitation - we can only query known collection names
                showInfo('Client SDK limitation: Cannot list all collections. Try querying known collections like "users", "products", "orders", etc.');
                
                // Show common collection names to try
                const commonCollections = ['users', 'products', 'orders', 'retailers', 'locations', 'campaigns', 'analytics'];
                let html = '<h4>Try these common collection names:</h4><ul>';
                commonCollections.forEach(name => {
                    html += `<li><button onclick="document.getElementById('collectionName').value='${name}'; queryCollection();">${name}</button></li>`;
                });
                html += '</ul>';
                
                showResult('collectionsResult', html);
            } catch (error) {
                showError('Error: ' + error.message);
            }
        };

        window.queryCollection = async function() {
            if (!db) {
                showError('Please initialize Firebase first');
                return;
            }

            const collectionName = document.getElementById('collectionName').value;
            const queryLimit = parseInt(document.getElementById('queryLimit').value) || 10;

            if (!collectionName) {
                showError('Please enter a collection name');
                return;
            }

            try {
                const collectionRef = collection(db, collectionName);
                const q = query(collectionRef, limit(queryLimit));
                const querySnapshot = await getDocs(q);

                if (querySnapshot.empty) {
                    showResult('queryResult', `No documents found in collection '${collectionName}'`);
                    return;
                }

                let html = `<h4>Found ${querySnapshot.size} documents in '${collectionName}':</h4>`;
                querySnapshot.forEach((doc, index) => {
                    html += `<div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 3px;">`;
                    html += `<strong>Document ${index + 1} - ID: ${doc.id}</strong><br>`;
                    html += `<pre>${JSON.stringify(doc.data(), null, 2)}</pre>`;
                    html += `</div>`;
                });

                showResult('queryResult', html);
            } catch (error) {
                showError('Error querying collection: ' + error.message);
            }
        };

        window.getDocument = async function() {
            if (!db) {
                showError('Please initialize Firebase first');
                return;
            }

            const collectionName = document.getElementById('docCollection').value;
            const docId = document.getElementById('docId').value;

            if (!collectionName || !docId) {
                showError('Please enter both collection name and document ID');
                return;
            }

            try {
                const docRef = doc(db, collectionName, docId);
                const docSnap = await getDoc(docRef);

                if (!docSnap.exists()) {
                    showResult('docResult', `Document '${docId}' not found in collection '${collectionName}'`);
                    return;
                }

                const html = `<h4>Document '${docId}' in '${collectionName}':</h4><pre>${JSON.stringify(docSnap.data(), null, 2)}</pre>`;
                showResult('docResult', html);
            } catch (error) {
                showError('Error getting document: ' + error.message);
            }
        };

        window.queryRealtimeDB = async function() {
            if (!rtdb) {
                showError('Please initialize Firebase first');
                return;
            }

            const path = document.getElementById('rtdbPath').value || '/';

            try {
                const dbRef = ref(rtdb, path);
                const snapshot = await get(dbRef);

                if (!snapshot.exists()) {
                    showResult('rtdbResult', `No data found at path '${path}'`);
                    return;
                }

                const html = `<h4>Data at '${path}':</h4><pre>${JSON.stringify(snapshot.val(), null, 2)}</pre>`;
                showResult('rtdbResult', html);
            } catch (error) {
                showError('Error querying Realtime Database: ' + error.message);
            }
        };

        function showResult(elementId, content) {
            const element = document.getElementById(elementId);
            element.innerHTML = content;
            element.style.display = 'block';
        }

        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            document.body.appendChild(errorDiv);
            setTimeout(() => errorDiv.remove(), 5000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            document.body.appendChild(successDiv);
            setTimeout(() => successDiv.remove(), 3000);
        }

        function showInfo(message) {
            const infoDiv = document.createElement('div');
            infoDiv.style.cssText = 'color: #0c5460; background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 10px; border-radius: 5px; margin-top: 10px;';
            infoDiv.textContent = message;
            document.body.appendChild(infoDiv);
            setTimeout(() => infoDiv.remove(), 8000);
        }

        // Auto-initialize on page load
        window.addEventListener('load', () => {
            initializeFirebase();
        });
    </script>
</body>
</html>
