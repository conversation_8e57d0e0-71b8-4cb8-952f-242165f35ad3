{"name": "ai-marketing-assistant-monorepo", "version": "1.0.0", "scripts": {"dev": "npm run dev:ui & npm run dev:platform", "dev:ui": "cd apps/ui && npm start", "dev:platform": "cd apps/platform && npm start", "dev:ui-only": "cd apps/ui && npm start", "dev:platform-only": "cd apps/platform && npm start", "install:all": "npm install && cd apps/ui && npm install && cd ../platform && npm install", "build": "cd apps/ui && npm run build && cd ../platform && npm run build", "test": "cd apps/ui && npm test && cd ../platform && npm test", "demo:smokey-enhancements": "cd apps/platform && node src/chats/demoEnhancements.js", "test:smokey-enhancements": "cd apps/platform && node -e \"require('./src/chats/testSmokeyEnhancements.ts').runTests()\""}, "dependencies": {"@sendgrid/mail": "^8.1.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "firebase-admin": "^13.4.0", "moment": "^2.30.1", "mysql2": "^3.14.1", "pdf-parse": "^1.1.1", "react-big-calendar": "^1.18.0", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-toastify": "^11.0.2", "stripe": "^17.7.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/react-big-calendar": "^1.16.1"}}