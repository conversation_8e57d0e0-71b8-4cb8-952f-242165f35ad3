#!/usr/bin/env node

/**
 * Simple Firebase Connection Test
 * 
 * This script tests your Firebase connection using the credentials from .env
 */

require('dotenv').config();
const admin = require('firebase-admin');

console.log('🔥 Firebase Connection Test');
console.log('===========================');

// Display environment variables (without sensitive data)
console.log('\n📋 Environment Variables:');
console.log(`FIREBASE_PROJECT_ID: ${process.env.FIREBASE_PROJECT_ID || '❌ Missing'}`);
console.log(`FIREBASE_CLIENT_EMAIL: ${process.env.FIREBASE_CLIENT_EMAIL || '❌ Missing'}`);
console.log(`FIREBASE_PRIVATE_KEY: ${process.env.FIREBASE_PRIVATE_KEY ? '✅ Present' : '❌ Missing'}`);
console.log(`FIREBASE_STORAGE_BUCKET: ${process.env.FIREBASE_STORAGE_BUCKET || '❌ Missing'}`);

// Test Firebase initialization
async function testFirebaseConnection() {
  try {
    console.log('\n🚀 Initializing Firebase Admin SDK...');
    
    // Create service account object
    const serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_PROJECT_ID,
      private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
      private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      client_id: process.env.FIREBASE_CLIENT_ID,
      auth_uri: "https://accounts.google.com/o/oauth2/auth",
      token_uri: "https://oauth2.googleapis.com/token",
      auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
      client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${encodeURIComponent(process.env.FIREBASE_CLIENT_EMAIL)}`
    };

    // Initialize Firebase Admin
    const app = admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      databaseURL: `https://${process.env.FIREBASE_PROJECT_ID}-default-rtdb.firebaseio.com/`,
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET
    });

    console.log('✅ Firebase Admin SDK initialized successfully!');
    
    // Test Firestore connection
    console.log('\n🗄️  Testing Firestore connection...');
    const db = admin.firestore();
    const collections = await db.listCollections();
    console.log(`✅ Firestore connected! Found ${collections.length} collections:`);
    collections.forEach((col, index) => {
      console.log(`   ${index + 1}. ${col.id}`);
    });
    
    // Test Auth connection
    console.log('\n👥 Testing Firebase Auth connection...');
    const listUsersResult = await admin.auth().listUsers(1);
    console.log(`✅ Firebase Auth connected! Total users: ${listUsersResult.users.length > 0 ? 'At least 1' : '0'}`);
    
    // Test Realtime Database connection
    console.log('\n📊 Testing Realtime Database connection...');
    try {
      const rtdb = admin.database();
      const ref = rtdb.ref('/');
      const snapshot = await ref.limitToFirst(1).once('value');
      console.log('✅ Realtime Database connected!');
      console.log(`   Data exists: ${snapshot.exists() ? 'Yes' : 'No'}`);
    } catch (rtdbError) {
      console.log('⚠️  Realtime Database connection failed (this is normal if not enabled)');
      console.log(`   Error: ${rtdbError.message}`);
    }
    
    // Test Storage connection
    console.log('\n💾 Testing Firebase Storage connection...');
    try {
      const bucket = admin.storage().bucket();
      const [files] = await bucket.getFiles({ maxResults: 1 });
      console.log('✅ Firebase Storage connected!');
      console.log(`   Files found: ${files.length > 0 ? 'Yes' : 'No'}`);
    } catch (storageError) {
      console.log('⚠️  Firebase Storage connection failed');
      console.log(`   Error: ${storageError.message}`);
    }
    
    console.log('\n🎉 Firebase connection test completed successfully!');
    console.log('\n📝 You can now use the following scripts to query your data:');
    console.log('   • node firebase-query-script.js all');
    console.log('   • node firebase-explorer.js');
    
  } catch (error) {
    console.error('\n❌ Firebase connection test failed:');
    console.error(`Error: ${error.message}`);
    
    if (error.code) {
      console.error(`Error Code: ${error.code}`);
    }
    
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Check that all Firebase environment variables are set correctly');
    console.log('2. Verify that the private key is properly formatted (with \\n for newlines)');
    console.log('3. Ensure the service account has the necessary permissions');
    console.log('4. Check that the Firebase project ID is correct');
    
    process.exit(1);
  }
}

// Run the test
testFirebaseConnection();
