#!/usr/bin/env node

/**
 * Interactive Firebase Database Explorer
 * 
 * This script provides an interactive way to explore your Firebase database
 * with a simple menu-driven interface.
 */

require('dotenv').config();
const admin = require('firebase-admin');
const readline = require('readline');

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Promisify readline question
function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

// Initialize Firebase Admin SDK
function initializeFirebase() {
  try {
    if (admin.apps.length > 0) {
      return admin.app();
    }

    const serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_PROJECT_ID,
      private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
      private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      client_id: process.env.FIREBASE_CLIENT_ID,
      auth_uri: "https://accounts.google.com/o/oauth2/auth",
      token_uri: "https://oauth2.googleapis.com/token",
      auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
      client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${encodeURIComponent(process.env.FIREBASE_CLIENT_EMAIL)}`
    };

    const app = admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      databaseURL: `https://${process.env.FIREBASE_PROJECT_ID}-default-rtdb.firebaseio.com/`,
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET
    });

    console.log('✅ Firebase initialized successfully');
    return app;
  } catch (error) {
    console.error('❌ Error initializing Firebase:', error.message);
    throw error;
  }
}

// Display main menu
function displayMenu() {
  console.log('\n🔥 Firebase Database Explorer');
  console.log('=============================');
  console.log('1. List Firestore collections');
  console.log('2. Query Firestore collection');
  console.log('3. Query Realtime Database');
  console.log('4. List Auth users');
  console.log('5. Get specific document by ID');
  console.log('6. Search collection by field');
  console.log('7. Show database statistics');
  console.log('0. Exit');
  console.log('=============================');
}

// List Firestore collections
async function listCollections() {
  try {
    const db = admin.firestore();
    const collections = await db.listCollections();
    
    if (collections.length === 0) {
      console.log('📭 No collections found');
      return [];
    }

    console.log(`\n📚 Found ${collections.length} collections:`);
    collections.forEach((col, index) => {
      console.log(`${index + 1}. ${col.id}`);
    });
    
    return collections.map(col => col.id);
  } catch (error) {
    console.error('❌ Error listing collections:', error.message);
    return [];
  }
}

// Query Firestore collection
async function queryCollection() {
  const collectionName = await question('\n📝 Enter collection name: ');
  const limitStr = await question('📊 Enter limit (default 10): ');
  const limit = parseInt(limitStr) || 10;

  try {
    const db = admin.firestore();
    const snapshot = await db.collection(collectionName).limit(limit).get();
    
    if (snapshot.empty) {
      console.log(`📭 No documents found in '${collectionName}'`);
      return;
    }

    console.log(`\n📄 Found ${snapshot.size} documents in '${collectionName}':`);
    snapshot.forEach((doc, index) => {
      console.log(`\n--- Document ${index + 1} ---`);
      console.log(`ID: ${doc.id}`);
      console.log('Data:', JSON.stringify(doc.data(), null, 2));
    });
  } catch (error) {
    console.error('❌ Error querying collection:', error.message);
  }
}

// Query Realtime Database
async function queryRealtimeDB() {
  const path = await question('\n📝 Enter path (default /): ') || '/';
  const limitStr = await question('📊 Enter limit (0 for no limit): ');
  const limit = parseInt(limitStr) || 0;

  try {
    const db = admin.database();
    let ref = db.ref(path);
    
    if (limit > 0) {
      ref = ref.limitToFirst(limit);
    }
    
    const snapshot = await ref.once('value');
    const data = snapshot.val();
    
    if (!data) {
      console.log(`📭 No data found at '${path}'`);
      return;
    }

    console.log(`\n📄 Data at '${path}':`);
    console.log(JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('❌ Error querying Realtime Database:', error.message);
  }
}

// List Auth users
async function listUsers() {
  const limitStr = await question('\n📊 Enter max users to show (default 10): ');
  const limit = parseInt(limitStr) || 10;

  try {
    const listUsersResult = await admin.auth().listUsers(limit);
    
    if (listUsersResult.users.length === 0) {
      console.log('👤 No users found');
      return;
    }

    console.log(`\n👥 Found ${listUsersResult.users.length} users:`);
    listUsersResult.users.forEach((user, index) => {
      console.log(`\n--- User ${index + 1} ---`);
      console.log(`UID: ${user.uid}`);
      console.log(`Email: ${user.email || 'N/A'}`);
      console.log(`Display Name: ${user.displayName || 'N/A'}`);
      console.log(`Email Verified: ${user.emailVerified}`);
      console.log(`Created: ${user.metadata.creationTime}`);
      console.log(`Last Sign In: ${user.metadata.lastSignInTime || 'Never'}`);
    });
  } catch (error) {
    console.error('❌ Error listing users:', error.message);
  }
}

// Get specific document
async function getDocument() {
  const collectionName = await question('\n📝 Enter collection name: ');
  const docId = await question('🆔 Enter document ID: ');

  try {
    const db = admin.firestore();
    const doc = await db.collection(collectionName).doc(docId).get();
    
    if (!doc.exists) {
      console.log(`📭 Document '${docId}' not found in '${collectionName}'`);
      return;
    }

    console.log(`\n📄 Document '${docId}' in '${collectionName}':`);
    console.log(JSON.stringify(doc.data(), null, 2));
  } catch (error) {
    console.error('❌ Error getting document:', error.message);
  }
}

// Search collection by field
async function searchCollection() {
  const collectionName = await question('\n📝 Enter collection name: ');
  const fieldName = await question('🔍 Enter field name to search: ');
  const fieldValue = await question('💭 Enter field value: ');
  const limitStr = await question('📊 Enter limit (default 10): ');
  const limit = parseInt(limitStr) || 10;

  try {
    const db = admin.firestore();
    const snapshot = await db.collection(collectionName)
      .where(fieldName, '==', fieldValue)
      .limit(limit)
      .get();
    
    if (snapshot.empty) {
      console.log(`📭 No documents found with ${fieldName} = '${fieldValue}'`);
      return;
    }

    console.log(`\n📄 Found ${snapshot.size} documents:`);
    snapshot.forEach((doc, index) => {
      console.log(`\n--- Document ${index + 1} ---`);
      console.log(`ID: ${doc.id}`);
      console.log('Data:', JSON.stringify(doc.data(), null, 2));
    });
  } catch (error) {
    console.error('❌ Error searching collection:', error.message);
  }
}

// Show database statistics
async function showStats() {
  console.log('\n📊 Database Statistics');
  console.log('=====================');

  try {
    // Firestore stats
    const db = admin.firestore();
    const collections = await db.listCollections();
    console.log(`📚 Firestore Collections: ${collections.length}`);
    
    for (const collection of collections.slice(0, 5)) { // Limit to first 5 collections
      try {
        const snapshot = await collection.limit(1).get();
        const countSnapshot = await collection.count().get();
        console.log(`  - ${collection.id}: ~${countSnapshot.data().count} documents`);
      } catch (error) {
        console.log(`  - ${collection.id}: Unable to count documents`);
      }
    }

    // Auth stats
    const listUsersResult = await admin.auth().listUsers(1000);
    console.log(`👥 Auth Users: ${listUsersResult.users.length}`);
    
  } catch (error) {
    console.error('❌ Error getting statistics:', error.message);
  }
}

// Main interactive loop
async function main() {
  try {
    console.log('🚀 Initializing Firebase Database Explorer...');
    initializeFirebase();
    
    while (true) {
      displayMenu();
      const choice = await question('\n🎯 Enter your choice (0-7): ');
      
      switch (choice) {
        case '1':
          await listCollections();
          break;
        case '2':
          await queryCollection();
          break;
        case '3':
          await queryRealtimeDB();
          break;
        case '4':
          await listUsers();
          break;
        case '5':
          await getDocument();
          break;
        case '6':
          await searchCollection();
          break;
        case '7':
          await showStats();
          break;
        case '0':
          console.log('👋 Goodbye!');
          rl.close();
          process.exit(0);
          break;
        default:
          console.log('❌ Invalid choice. Please try again.');
      }
      
      await question('\n⏸️  Press Enter to continue...');
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    rl.close();
    process.exit(1);
  }
}

// Handle cleanup
process.on('SIGINT', () => {
  console.log('\n👋 Goodbye!');
  rl.close();
  process.exit(0);
});

// Run the script
if (require.main === module) {
  main();
}
