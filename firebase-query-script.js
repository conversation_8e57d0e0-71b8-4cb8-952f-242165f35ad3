#!/usr/bin/env node

/**
 * Firebase Database Query Script
 * 
 * This script allows you to query your Firebase database using the credentials
 * from your .env file. It supports both Firestore and Realtime Database queries.
 */

require('dotenv').config();
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
function initializeFirebase() {
  try {
    // Check if Firebase is already initialized
    if (admin.apps.length > 0) {
      console.log('Using existing Firebase app');
      return admin.app();
    }

    // Create service account object from environment variables
    const serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_PROJECT_ID,
      private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
      private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      client_id: process.env.FIREBASE_CLIENT_ID,
      auth_uri: "https://accounts.google.com/o/oauth2/auth",
      token_uri: "https://oauth2.googleapis.com/token",
      auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
      client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${encodeURIComponent(process.env.FIREBASE_CLIENT_EMAIL)}`
    };

    // Initialize Firebase Admin
    const app = admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      databaseURL: `https://${process.env.FIREBASE_PROJECT_ID}-default-rtdb.firebaseio.com/`,
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET
    });

    console.log('✅ Firebase Admin SDK initialized successfully');
    console.log(`📊 Project ID: ${process.env.FIREBASE_PROJECT_ID}`);
    console.log(`📧 Client Email: ${process.env.FIREBASE_CLIENT_EMAIL}`);
    
    return app;
  } catch (error) {
    console.error('❌ Error initializing Firebase:', error.message);
    process.exit(1);
  }
}

// Query Firestore collections
async function queryFirestore(collectionName, limit = 10) {
  try {
    console.log(`\n🔍 Querying Firestore collection: ${collectionName}`);
    
    const db = admin.firestore();
    const snapshot = await db.collection(collectionName).limit(limit).get();
    
    if (snapshot.empty) {
      console.log(`📭 No documents found in collection '${collectionName}'`);
      return [];
    }

    const documents = [];
    snapshot.forEach(doc => {
      documents.push({
        id: doc.id,
        data: doc.data()
      });
    });

    console.log(`📄 Found ${documents.length} documents:`);
    documents.forEach((doc, index) => {
      console.log(`\n--- Document ${index + 1} ---`);
      console.log(`ID: ${doc.id}`);
      console.log('Data:', JSON.stringify(doc.data, null, 2));
    });

    return documents;
  } catch (error) {
    console.error(`❌ Error querying Firestore collection '${collectionName}':`, error.message);
    return [];
  }
}

// List all Firestore collections
async function listFirestoreCollections() {
  try {
    console.log('\n📚 Listing all Firestore collections...');
    
    const db = admin.firestore();
    const collections = await db.listCollections();
    
    if (collections.length === 0) {
      console.log('📭 No collections found in Firestore');
      return [];
    }

    console.log(`📄 Found ${collections.length} collections:`);
    collections.forEach((collection, index) => {
      console.log(`${index + 1}. ${collection.id}`);
    });

    return collections.map(col => col.id);
  } catch (error) {
    console.error('❌ Error listing Firestore collections:', error.message);
    return [];
  }
}

// Query Realtime Database
async function queryRealtimeDatabase(path = '/', limit = 10) {
  try {
    console.log(`\n🔍 Querying Realtime Database path: ${path}`);
    
    const db = admin.database();
    const ref = db.ref(path);
    
    let query = ref;
    if (limit && limit > 0) {
      query = ref.limitToFirst(limit);
    }
    
    const snapshot = await query.once('value');
    const data = snapshot.val();
    
    if (!data) {
      console.log(`📭 No data found at path '${path}'`);
      return null;
    }

    console.log(`📄 Data at '${path}':`);
    console.log(JSON.stringify(data, null, 2));
    
    return data;
  } catch (error) {
    console.error(`❌ Error querying Realtime Database path '${path}':`, error.message);
    return null;
  }
}

// Get Firebase Auth users
async function listAuthUsers(maxResults = 10) {
  try {
    console.log(`\n👥 Listing Firebase Auth users (max ${maxResults})...`);
    
    const listUsersResult = await admin.auth().listUsers(maxResults);
    
    if (listUsersResult.users.length === 0) {
      console.log('👤 No users found');
      return [];
    }

    console.log(`👥 Found ${listUsersResult.users.length} users:`);
    listUsersResult.users.forEach((user, index) => {
      console.log(`\n--- User ${index + 1} ---`);
      console.log(`UID: ${user.uid}`);
      console.log(`Email: ${user.email || 'N/A'}`);
      console.log(`Display Name: ${user.displayName || 'N/A'}`);
      console.log(`Email Verified: ${user.emailVerified}`);
      console.log(`Created: ${user.metadata.creationTime}`);
      console.log(`Last Sign In: ${user.metadata.lastSignInTime || 'Never'}`);
    });

    return listUsersResult.users;
  } catch (error) {
    console.error('❌ Error listing Auth users:', error.message);
    return [];
  }
}

// Main function to run queries
async function main() {
  console.log('🚀 Firebase Database Query Script');
  console.log('==================================');

  // Initialize Firebase
  const app = initializeFirebase();

  // Get command line arguments
  const args = process.argv.slice(2);
  const command = args[0];
  const param1 = args[1];
  const param2 = args[2];

  try {
    switch (command) {
      case 'collections':
      case 'list-collections':
        await listFirestoreCollections();
        break;
        
      case 'firestore':
      case 'fs':
        if (!param1) {
          console.log('❌ Please provide a collection name');
          console.log('Usage: node firebase-query-script.js firestore <collection-name> [limit]');
          process.exit(1);
        }
        const limit = param2 ? parseInt(param2) : 10;
        await queryFirestore(param1, limit);
        break;
        
      case 'realtime':
      case 'rt':
        const path = param1 || '/';
        const rtLimit = param2 ? parseInt(param2) : 10;
        await queryRealtimeDatabase(path, rtLimit);
        break;
        
      case 'users':
      case 'auth':
        const maxUsers = param1 ? parseInt(param1) : 10;
        await listAuthUsers(maxUsers);
        break;
        
      case 'all':
        console.log('\n🔍 Running comprehensive Firebase data check...');
        await listFirestoreCollections();
        await queryRealtimeDatabase('/', 5);
        await listAuthUsers(5);
        break;
        
      default:
        console.log('\n📖 Usage:');
        console.log('  node firebase-query-script.js collections                    # List all Firestore collections');
        console.log('  node firebase-query-script.js firestore <collection> [limit] # Query Firestore collection');
        console.log('  node firebase-query-script.js realtime [path] [limit]        # Query Realtime Database');
        console.log('  node firebase-query-script.js users [limit]                  # List Auth users');
        console.log('  node firebase-query-script.js all                            # Run all queries');
        console.log('\n📝 Examples:');
        console.log('  node firebase-query-script.js collections');
        console.log('  node firebase-query-script.js firestore users 5');
        console.log('  node firebase-query-script.js realtime /users 3');
        console.log('  node firebase-query-script.js users 10');
        break;
    }
  } catch (error) {
    console.error('❌ Error executing command:', error.message);
    process.exit(1);
  }

  console.log('\n✅ Query completed successfully');
  process.exit(0);
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = {
  initializeFirebase,
  queryFirestore,
  listFirestoreCollections,
  queryRealtimeDatabase,
  listAuthUsers
};
